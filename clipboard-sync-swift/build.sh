#!/bin/bash

# Clipboard Sync Swift版本构建脚本

set -e

echo "🚀 开始构建 Clipboard Sync Swift版本..."

# 检查Xcode是否可用
if ! command -v xcodebuild &> /dev/null; then
    echo "❌ 错误: 需要安装Xcode或Xcode Command Line Tools"
    echo "   请从App Store安装Xcode，或运行: xcode-select --install"
    exit 1
fi

# 检查项目文件
if [ ! -f "ClipboardSync.xcodeproj/project.pbxproj" ]; then
    echo "❌ 错误: 找不到Xcode项目文件"
    exit 1
fi

# 清理之前的构建
echo "🧹 清理之前的构建..."
rm -rf build/
rm -rf DerivedData/

# 构建项目
echo "🔨 构建项目..."
xcodebuild \
    -project ClipboardSync.xcodeproj \
    -scheme ClipboardSync \
    -configuration Release \
    -derivedDataPath ./DerivedData \
    build

if [ $? -eq 0 ]; then
    echo "✅ 构建成功!"
    
    # 查找构建的应用
    APP_PATH=$(find ./DerivedData -name "ClipboardSync.app" -type d | head -1)
    
    if [ -n "$APP_PATH" ]; then
        echo "📱 应用位置: $APP_PATH"
        
        # --- 创建DMG安装包 ---
        echo "📦 正在创建DMG安装包..."
        
        # 从Info.plist获取版本号
        VERSION=$(/usr/libexec/PlistBuddy -c "Print CFBundleShortVersionString" "$APP_PATH/Contents/Info.plist")
        if [ -z "$VERSION" ]; then
            VERSION="1.0.0" # 如果未找到版本号，则使用默认值
        fi
        
        DMG_FINAL_NAME="ClipboardSync-$VERSION.dmg"
        DMG_TEMP_NAME="ClipboardSync_temp.dmg"
        VOLUME_NAME="Clipboard Sync $VERSION"
        
        # 删除旧的DMG文件
        rm -f "$DMG_FINAL_NAME"
        
        # 创建一个临时的可读写DMG
        hdiutil create -o "$DMG_TEMP_NAME" -size 100m -volname "$VOLUME_NAME" -fs HFS+
        
        # 挂载临时DMG
        hdiutil attach "$DMG_TEMP_NAME"
        
        # 将.app文件复制到挂载的卷中
        cp -R "$APP_PATH" "/Volumes/$VOLUME_NAME/"
        
        # 创建指向Applications目录的符号链接
        ln -s /Applications "/Volumes/$VOLUME_NAME/Applications"
        
        # 卸载卷
        hdiutil detach "/Volumes/$VOLUME_NAME"
        
        # 将临时DMG转换为压缩的只读DMG
        hdiutil convert "$DMG_TEMP_NAME" -format UDZO -o "$DMG_FINAL_NAME"
        
        # 删除临时文件
        rm "$DMG_TEMP_NAME"
        
        echo "✅ DMG创建成功: $DMG_FINAL_NAME"
        echo "   文件位于: $(pwd)/$DMG_FINAL_NAME"

    else
        echo "⚠️  警告: 找不到构建的应用文件"
    fi
else
    echo "❌ 构建失败!"
    exit 1
fi

echo ""
echo "🎯 下一步:"
echo "   1. 运行应用: open ./ClipboardSync.app"
echo "   2. 配置ntfy主题和服务器"
echo "   3. 启动剪贴板同步"
echo ""
echo "📚 更多信息请查看 README.md"
