import SwiftUI

struct ConfigView: View {
    @EnvironmentObject var configManager: ConfigManager
    @State private var showingAlert = false
    @State private var alertMessage = ""
    @State private var alertType: AlertType = .success

    enum AlertType {
        case success, error

        var icon: String {
            switch self {
            case .success: return "checkmark.circle.fill"
            case .error: return "xmark.circle.fill"
            }
        }

        var color: Color {
            switch self {
            case .success: return .green
            case .error: return .red
            }
        }
    }

    var body: some View {
        ScrollView {
            LazyVStack(spacing: 24) {
                // Sender configuration section
                ConfigurationSection(
                    title: "Sender Configuration",
                    subtitle: "Configure outgoing clipboard sync",
                    icon: "arrow.up.circle",
                    color: .blue
                ) {
                    VStack(spacing: 16) {
                        // Enable toggle with modern styling
                        HStack {
                            VStack(alignment: .leading, spacing: 4) {
                                Text("Enable Sender")
                                    .font(.subheadline)
                                    .fontWeight(.medium)

                                Text("Send clipboard changes to other devices")
                                    .font(.caption)
                                    .foregroundStyle(.secondary)
                            }

                            Spacer()

                            Toggle("", isOn: $configManager.config.sender.enabled)
                                .toggleStyle(.switch)
                        }

                        Divider()

                        // URL field
                        ConfigField(
                            title: "Ntfy Topic URL",
                            subtitle: "The notification service endpoint",
                            icon: "link"
                        ) {
                            TextField("https://ntfy.sh/your_topic_here", text: $configManager.config.sender.ntfyTopicUrl)
                                .textFieldStyle(.roundedBorder)
                        }

                        // Timing settings
                        HStack(spacing: 16) {
                            ConfigField(
                                title: "Poll Interval",
                                subtitle: "Seconds between checks",
                                icon: "timer"
                            ) {
                                TextField("1.0", value: $configManager.config.sender.pollIntervalSeconds, format: .number)
                                    .textFieldStyle(.roundedBorder)
                            }

                            ConfigField(
                                title: "Request Timeout",
                                subtitle: "Network timeout in seconds",
                                icon: "clock"
                            ) {
                                TextField("15", value: $configManager.config.sender.requestTimeoutSeconds, format: .number)
                                    .textFieldStyle(.roundedBorder)
                            }
                        }

                        // Filename prefix
                        ConfigField(
                            title: "Filename Prefix",
                            subtitle: "Prefix for uploaded files",
                            icon: "doc.text"
                        ) {
                            TextField("clipboard_content_", text: $configManager.config.sender.filenamePrefix)
                                .textFieldStyle(.roundedBorder)
                        }
                    }
                }

                // Receiver configuration section
                ConfigurationSection(
                    title: "Receiver Configuration",
                    subtitle: "Configure incoming clipboard sync",
                    icon: "arrow.down.circle",
                    color: .green
                ) {
                    VStack(spacing: 16) {
                        // Enable toggle
                        HStack {
                            VStack(alignment: .leading, spacing: 4) {
                                Text("Enable Receiver")
                                    .font(.subheadline)
                                    .fontWeight(.medium)

                                Text("Receive clipboard changes from other devices")
                                    .font(.caption)
                                    .foregroundStyle(.secondary)
                            }

                            Spacer()

                            Toggle("", isOn: $configManager.config.receiver.enabled)
                                .toggleStyle(.switch)
                        }

                        Divider()

                        // Server and topic settings
                        HStack(spacing: 16) {
                            ConfigField(
                                title: "Ntfy Server",
                                subtitle: "Notification server hostname",
                                icon: "server.rack"
                            ) {
                                TextField("ntfy.sh", text: $configManager.config.receiver.ntfyServer)
                                    .textFieldStyle(.roundedBorder)
                            }

                            ConfigField(
                                title: "Ntfy Topic",
                                subtitle: "Topic to subscribe to",
                                icon: "antenna.radiowaves.left.and.right"
                            ) {
                                TextField("your_receive_topic_here", text: $configManager.config.receiver.ntfyTopic)
                                    .textFieldStyle(.roundedBorder)
                            }
                        }

                        // Connection settings
                        HStack(spacing: 16) {
                            ConfigField(
                                title: "Reconnect Delay",
                                subtitle: "Seconds between reconnect attempts",
                                icon: "arrow.clockwise"
                            ) {
                                TextField("5", value: $configManager.config.receiver.reconnectDelaySeconds, format: .number)
                                    .textFieldStyle(.roundedBorder)
                            }

                            ConfigField(
                                title: "Request Timeout",
                                subtitle: "Network timeout in seconds",
                                icon: "clock"
                            ) {
                                TextField("15", value: $configManager.config.receiver.requestTimeoutSeconds, format: .number)
                                    .textFieldStyle(.roundedBorder)
                            }
                        }
                    }
                }

                // Advanced settings section
                ConfigurationSection(
                    title: "Advanced Settings",
                    subtitle: "Logging and platform-specific options",
                    icon: "gearshape.2",
                    color: .purple
                ) {
                    VStack(spacing: 16) {
                        // Log level picker
                        HStack {
                            VStack(alignment: .leading, spacing: 4) {
                                Text("Log Level")
                                    .font(.subheadline)
                                    .fontWeight(.medium)

                                Text("Verbosity of application logging")
                                    .font(.caption)
                                    .foregroundStyle(.secondary)
                            }

                            Spacer()

                            Picker("Log Level", selection: $configManager.config.logging.level) {
                                Label("Debug", systemImage: "ladybug").tag("DEBUG")
                                Label("Info", systemImage: "info.circle").tag("INFO")
                                Label("Warning", systemImage: "exclamationmark.triangle").tag("WARNING")
                                Label("Error", systemImage: "xmark.circle").tag("ERROR")
                            }
                            .pickerStyle(.menu)
                            .frame(width: 120)
                        }

                        Divider()

                        // macOS specific settings
                        HStack {
                            VStack(alignment: .leading, spacing: 4) {
                                Text("macOS Image Support")
                                    .font(.subheadline)
                                    .fontWeight(.medium)

                                Text("Enable clipboard image synchronization")
                                    .font(.caption)
                                    .foregroundStyle(.secondary)
                            }

                            Spacer()

                            Toggle("", isOn: $configManager.config.macos.imageSupport)
                                .toggleStyle(.switch)
                        }
                    }
                }

                // Action buttons section
                VStack(spacing: 16) {
                    HStack(spacing: 12) {
                        Button(action: loadFromYAML) {
                            Label("Reload from YAML", systemImage: "arrow.clockwise")
                        }
                        .buttonStyle(.bordered)
                        .controlSize(.large)

                        Button(action: saveConfiguration) {
                            Label("Save Configuration", systemImage: "checkmark.circle")
                        }
                        .buttonStyle(.borderedProminent)
                        .controlSize(.large)
                    }

                    Text("Changes are automatically saved when modified")
                        .font(.caption)
                        .foregroundStyle(.secondary)
                }
                .padding(.horizontal, 24)
            }
            .padding(.horizontal, 24)
            .padding(.vertical, 20)
        }
        .alert("Configuration", isPresented: $showingAlert) {
            Button("OK") { }
        } message: {
            HStack(spacing: 8) {
                Image(systemName: alertType.icon)
                    .foregroundStyle(alertType.color)
                Text(alertMessage)
            }
        }
    }

    private func loadFromYAML() {
        if configManager.loadFromYAMLFile() {
            alertMessage = "Configuration loaded from YAML file successfully!"
            alertType = .success
        } else {
            alertMessage = "Failed to load configuration from YAML file."
            alertType = .error
        }
        showingAlert = true
    }

    private func saveConfiguration() {
        if configManager.saveConfiguration() {
            alertMessage = "Configuration saved successfully!"
            alertType = .success
        } else {
            alertMessage = "Failed to save configuration."
            alertType = .error
        }
        showingAlert = true
    }
}

// MARK: - Supporting Views

struct ConfigurationSection<Content: View>: View {
    let title: String
    let subtitle: String
    let icon: String
    let color: Color
    @ViewBuilder let content: Content

    var body: some View {
        VStack(spacing: 0) {
            // Section header
            HStack {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundStyle(color.gradient)
                    .symbolRenderingMode(.hierarchical)

                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.headline)
                        .fontWeight(.semibold)

                    Text(subtitle)
                        .font(.caption)
                        .foregroundStyle(.secondary)
                }

                Spacer()
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)

            Divider()

            // Section content
            content
                .padding(.horizontal, 20)
                .padding(.bottom, 20)
        }
        .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(.quaternary, lineWidth: 0.5)
        )
    }
}

struct ConfigField<Content: View>: View {
    let title: String
    let subtitle: String
    let icon: String
    @ViewBuilder let content: Content

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.caption)
                    .foregroundStyle(.secondary)
                    .frame(width: 12)

                VStack(alignment: .leading, spacing: 1) {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.medium)

                    Text(subtitle)
                        .font(.caption2)
                        .foregroundStyle(.secondary)
                }
            }

            content
        }
    }
}
