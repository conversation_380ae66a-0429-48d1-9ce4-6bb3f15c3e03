import SwiftUI

struct StatusView: View {
    @EnvironmentObject var configManager: ConfigManager
    @EnvironmentObject var clipboardManager: ClipboardManager
    @EnvironmentObject var networkManager: NetworkManager

    var body: some View {
        ScrollView {
            LazyVStack(spacing: 24) {
                // Main status card with modern design
                VStack(spacing: 0) {
                    // Header
                    HStack {
                        Image(systemName: "antenna.radiowaves.left.and.right")
                            .font(.title2)
                            .foregroundStyle(.blue.gradient)
                            .symbolRenderingMode(.hierarchical)

                        VStack(alignment: .leading, spacing: 2) {
                            Text("Sync Status")
                                .font(.headline)
                                .fontWeight(.semibold)

                            Text("Real-time clipboard synchronization")
                                .font(.caption)
                                .foregroundStyle(.secondary)
                        }

                        Spacer()

                        // Main status indicator
                        HStack(spacing: 8) {
                            Image(systemName: clipboardManager.isRunning ? "checkmark.circle.fill" : "xmark.circle.fill")
                                .foregroundStyle(clipboardManager.isRunning ? .green : .red)
                                .font(.title3)
                                .symbolRenderingMode(.hierarchical)

                            VStack(alignment: .trailing, spacing: 1) {
                                Text(clipboardManager.isRunning ? "Active" : "Inactive")
                                    .font(.subheadline)
                                    .fontWeight(.semibold)
                                    .foregroundStyle(clipboardManager.isRunning ? .green : .red)

                                Text(clipboardManager.isRunning ? "Monitoring clipboard" : "Sync disabled")
                                    .font(.caption2)
                                    .foregroundStyle(.secondary)
                            }
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)

                    Divider()

                    // Status details
                    VStack(spacing: 16) {
                        // Service status row
                        HStack(spacing: 16) {
                            StatusCard(
                                title: "Sender",
                                subtitle: "Outgoing sync",
                                status: configManager.config.sender.enabled ? .enabled : .disabled,
                                icon: "arrow.up.circle"
                            )

                            StatusCard(
                                title: "Receiver",
                                subtitle: "Incoming sync",
                                status: configManager.config.receiver.enabled ? .enabled : .disabled,
                                icon: "arrow.down.circle"
                            )
                        }

                        if clipboardManager.isRunning {
                            // Connection status
                            HStack(spacing: 16) {
                                StatusCard(
                                    title: "WebSocket",
                                    subtitle: "Real-time connection",
                                    status: networkManager.isWebSocketConnected ? .connected : .disconnected,
                                    icon: "wifi"
                                )

                                StatusCard(
                                    title: "Last Activity",
                                    subtitle: clipboardManager.lastActivity.isEmpty ? "No recent activity" : clipboardManager.lastActivity,
                                    status: .info,
                                    icon: "clock"
                                )
                            }
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 20)
                }
                .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(.quaternary, lineWidth: 0.5)
                )

                // Statistics section with modern cards
                VStack(spacing: 0) {
                    HStack {
                        Image(systemName: "chart.bar")
                            .font(.title2)
                            .foregroundStyle(.purple.gradient)
                            .symbolRenderingMode(.hierarchical)

                        VStack(alignment: .leading, spacing: 2) {
                            Text("Statistics")
                                .font(.headline)
                                .fontWeight(.semibold)

                            Text("Usage metrics and performance")
                                .font(.caption)
                                .foregroundStyle(.secondary)
                        }

                        Spacer()

                        Button("Reset") {
                            clipboardManager.resetStatistics()
                            networkManager.resetStatistics()
                        }
                        .buttonStyle(.bordered)
                        .controlSize(.small)
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)

                    Divider()

                    // Statistics grid
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 16) {
                        StatisticCard(
                            title: "Sent",
                            value: "\(clipboardManager.messagesSent)",
                            icon: "arrow.up.circle.fill",
                            color: .blue
                        )

                        StatisticCard(
                            title: "Received",
                            value: "\(clipboardManager.messagesReceived)",
                            icon: "arrow.down.circle.fill",
                            color: .green
                        )

                        StatisticCard(
                            title: "Errors",
                            value: "\(networkManager.connectionErrors)",
                            icon: "exclamationmark.triangle.fill",
                            color: .red
                        )
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 20)
                }
                .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(.quaternary, lineWidth: 0.5)
                )
            }
            .padding(.horizontal, 24)
            .padding(.vertical, 20)
        }
    }
}

// MARK: - Supporting Views

struct StatusCard: View {
    let title: String
    let subtitle: String
    let status: StatusType
    let icon: String

    enum StatusType {
        case enabled, disabled, connected, disconnected, info

        var color: Color {
            switch self {
            case .enabled, .connected: return .green
            case .disabled, .disconnected: return .red
            case .info: return .blue
            }
        }

        var text: String {
            switch self {
            case .enabled: return "Enabled"
            case .disabled: return "Disabled"
            case .connected: return "Connected"
            case .disconnected: return "Disconnected"
            case .info: return ""
            }
        }
    }

    var body: some View {
        VStack(spacing: 8) {
            HStack {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundStyle(status.color.gradient)
                    .symbolRenderingMode(.hierarchical)

                Spacer()

                if status != .info {
                    Image(systemName: status == .enabled || status == .connected ? "checkmark.circle.fill" : "xmark.circle.fill")
                        .font(.caption)
                        .foregroundStyle(status.color)
                }
            }

            VStack(alignment: .leading, spacing: 2) {
                HStack {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                    Spacer()
                }

                HStack {
                    Text(status == .info ? subtitle : status.text)
                        .font(.caption)
                        .foregroundStyle(status == .info ? .secondary : status.color)
                        .lineLimit(2)
                    Spacer()
                }
            }
        }
        .padding(12)
        .background(.quaternary.opacity(0.3), in: RoundedRectangle(cornerRadius: 8))
        .overlay(
            RoundedRectangle(cornerRadius: 8)
                .stroke(.quaternary, lineWidth: 0.5)
        )
    }
}

struct StatisticCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color

    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundStyle(color.gradient)
                .symbolRenderingMode(.hierarchical)

            Text(value)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundStyle(color)

            Text(title)
                .font(.caption)
                .foregroundStyle(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 16)
        .background(.quaternary.opacity(0.3), in: RoundedRectangle(cornerRadius: 8))
        .overlay(
            RoundedRectangle(cornerRadius: 8)
                .stroke(.quaternary, lineWidth: 0.5)
        )
    }
}
