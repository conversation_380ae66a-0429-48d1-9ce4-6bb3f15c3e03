import SwiftUI

struct LogView: View {
    @EnvironmentObject var clipboardManager: ClipboardManager
    @State private var autoScroll = true
    @State private var selectedLogLevel: LogLevel? = nil
    @State private var searchText = ""

    var filteredLogs: [LogEntry] {
        var logs = clipboardManager.logs

        // Filter by log level
        if let selectedLevel = selectedLogLevel {
            logs = logs.filter { $0.level == selectedLevel }
        }

        // Filter by search text
        if !searchText.isEmpty {
            logs = logs.filter { $0.message.localizedCaseInsensitiveContains(searchText) }
        }

        return logs
    }

    var body: some View {
        VStack(spacing: 0) {
            // Modern toolbar
            VStack(spacing: 12) {
                // Header with icon and title
                HStack {
                    Image(systemName: "list.bullet.rectangle")
                        .font(.title2)
                        .foregroundStyle(.orange.gradient)
                        .symbolRenderingMode(.hierarchical)

                    VStack(alignment: .leading, spacing: 2) {
                        Text("Activity Log")
                            .font(.headline)
                            .fontWeight(.semibold)

                        Text("\(filteredLogs.count) entries")
                            .font(.caption)
                            .foregroundStyle(.secondary)
                    }

                    Spacer()

                    // Controls
                    HStack(spacing: 12) {
                        Toggle("Auto Scroll", isOn: $autoScroll)
                            .toggleStyle(.switch)
                            .controlSize(.small)

                        Button(action: { clipboardManager.clearLogs() }) {
                            Label("Clear", systemImage: "trash")
                        }
                        .buttonStyle(.bordered)
                        .controlSize(.small)
                    }
                }

                // Search and filter bar
                HStack(spacing: 12) {
                    // Search field
                    HStack {
                        Image(systemName: "magnifyingglass")
                            .foregroundStyle(.secondary)
                            .font(.caption)

                        TextField("Search logs...", text: $searchText)
                            .textFieldStyle(.plain)
                    }
                    .padding(.horizontal, 8)
                    .padding(.vertical, 6)
                    .background(.quaternary.opacity(0.5), in: RoundedRectangle(cornerRadius: 6))

                    // Log level filter
                    Picker("Filter", selection: $selectedLogLevel) {
                        Text("All Levels").tag(nil as LogLevel?)
                        ForEach(LogLevel.allCases, id: \.self) { level in
                            Label(level.displayName, systemImage: level.icon).tag(level as LogLevel?)
                        }
                    }
                    .pickerStyle(.menu)
                    .frame(width: 120)
                }
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
            .background(.regularMaterial)

            Divider()

            // Log content with modern styling
            ScrollViewReader { proxy in
                ScrollView {
                    LazyVStack(spacing: 0) {
                        if filteredLogs.isEmpty {
                            // Empty state
                            VStack(spacing: 16) {
                                Image(systemName: "doc.text")
                                    .font(.system(size: 48))
                                    .foregroundStyle(.quaternary)

                                VStack(spacing: 4) {
                                    Text("No Log Entries")
                                        .font(.headline)
                                        .fontWeight(.medium)

                                    Text(clipboardManager.logs.isEmpty ?
                                         "Start sync to see activity logs" :
                                         "No entries match your filter criteria")
                                        .font(.subheadline)
                                        .foregroundStyle(.secondary)
                                        .multilineTextAlignment(.center)
                                }
                            }
                            .frame(maxWidth: .infinity, maxHeight: .infinity)
                            .padding(.vertical, 60)
                        } else {
                            ForEach(Array(filteredLogs.enumerated()), id: \.offset) { index, log in
                                ModernLogEntryView(log: log)
                                    .id(index)

                                if index < filteredLogs.count - 1 {
                                    Divider()
                                        .padding(.leading, 60)
                                }
                            }
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 12)
                }
                .background(.regularMaterial)
                .onChange(of: clipboardManager.logs.count) { _ in
                    if autoScroll && !filteredLogs.isEmpty {
                        withAnimation(.easeOut(duration: 0.3)) {
                            proxy.scrollTo(filteredLogs.count - 1, anchor: .bottom)
                        }
                    }
                }
            }
        }
    }
}


struct ModernLogEntryView: View {
    let log: LogEntry

    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            // Log level indicator with icon
            VStack(spacing: 4) {
                Image(systemName: log.level.icon)
                    .font(.caption)
                    .foregroundStyle(log.level.color)
                    .frame(width: 16, height: 16)
                    .background(log.level.color.opacity(0.1), in: Circle())

                Text(log.timestamp, style: .time)
                    .font(.caption2)
                    .foregroundStyle(.tertiary)
                    .monospacedDigit()
            }
            .frame(width: 40)

            // Message content
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(log.level.displayName)
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundStyle(log.level.color)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(log.level.color.opacity(0.1), in: Capsule())

                    Spacer()

                    Text(log.timestamp, style: .date)
                        .font(.caption2)
                        .foregroundStyle(.secondary)
                }

                Text(log.message)
                    .font(.callout)
                    .foregroundStyle(.primary)
                    .textSelection(.enabled)
                    .fixedSize(horizontal: false, vertical: true)
            }
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(log.level.backgroundColor)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 8)
                .stroke(log.level.color.opacity(0.2), lineWidth: 0.5)
        )
    }
}

struct LogEntry {
    let timestamp: Date
    let level: LogLevel
    let message: String

    init(level: LogLevel, message: String) {
        self.timestamp = Date()
        self.level = level
        self.message = message
    }
}

enum LogLevel: String, CaseIterable, Hashable {
    case debug = "DEBUG"
    case info = "INFO"
    case warning = "WARN"
    case error = "ERROR"

    var displayName: String {
        switch self {
        case .debug: return "Debug"
        case .info: return "Info"
        case .warning: return "Warning"
        case .error: return "Error"
        }
    }

    var icon: String {
        switch self {
        case .debug: return "ladybug"
        case .info: return "info.circle"
        case .warning: return "exclamationmark.triangle"
        case .error: return "xmark.circle"
        }
    }

    var color: Color {
        switch self {
        case .debug: return .secondary
        case .info: return .blue
        case .warning: return .orange
        case .error: return .red
        }
    }

    var backgroundColor: Color {
        switch self {
        case .debug: return .clear
        case .info: return .blue.opacity(0.05)
        case .warning: return .orange.opacity(0.05)
        case .error: return .red.opacity(0.05)
        }
    }
}
