import SwiftUI

struct ContentView: View {
    @EnvironmentObject var configManager: ConfigManager
    @EnvironmentObject var clipboardManager: ClipboardManager
    @EnvironmentObject var networkManager: NetworkManager
    @EnvironmentObject var statusBarManager: StatusBarManager

    @State private var selectedTab = 0

    var body: some View {
        NavigationSplitView {
            // Sidebar with modern navigation
            VStack(spacing: 0) {
                // App header with improved spacing
                VStack(spacing: 16) {
                    // App icon and title with better alignment
                    HStack(spacing: 12) {
                        Image(systemName: "doc.on.clipboard")
                            .font(.title2)
                            .foregroundStyle(.blue.gradient)
                            .symbolRenderingMode(.hierarchical)
                            .frame(width: 24, height: 24)

                        VStack(alignment: .leading, spacing: 3) {
                            Text("Clipboard")
                                .font(.headline)
                                .fontWeight(.semibold)
                                .lineLimit(1)

                            Text("Sync")
                                .font(.headline)
                                .fontWeight(.semibold)
                                .lineLimit(1)
                        }

                        Spacer(minLength: 0)
                    }

                    // Subtitle with better spacing
                    HStack {
                        Text("Cross-device clipboard sharing")
                            .font(.caption)
                            .foregroundStyle(.secondary)
                            .lineLimit(2)
                            .multilineTextAlignment(.leading)

                        Spacer(minLength: 0)
                    }

                    // Status indicator with improved design
                    HStack(spacing: 8) {
                        Image(systemName: clipboardManager.isRunning ? "checkmark.circle.fill" : "xmark.circle.fill")
                            .foregroundStyle(clipboardManager.isRunning ? .green : .red)
                            .font(.caption)

                        Text(clipboardManager.isRunning ? "Active" : "Inactive")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundStyle(clipboardManager.isRunning ? .green : .red)

                        Spacer(minLength: 0)
                    }
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 20)
                .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))
                .padding(.horizontal, 12)
                .padding(.top, 12)

                // Navigation list
                List(selection: $selectedTab) {
                    Section {
                        NavigationLink(value: 0) {
                            Label("Overview", systemImage: "gauge.with.dots.needle.67percent")
                        }
                        .tag(0)

                        NavigationLink(value: 1) {
                            Label("Configuration", systemImage: "gearshape")
                        }
                        .tag(1)

                        NavigationLink(value: 2) {
                            Label("Activity", systemImage: "list.bullet.rectangle")
                        }
                        .tag(2)
                    }
                }
                .listStyle(.sidebar)
                .navigationSplitViewColumnWidth(min: 200, ideal: 220, max: 250)
            }
        } detail: {
            // Main content area with modern styling
            Group {
                switch selectedTab {
                case 0:
                    StatusView()
                case 1:
                    ConfigView()
                case 2:
                    LogView()
                default:
                    StatusView()
                }
            }
            .navigationTitle(navigationTitle)
            .toolbar {
                ToolbarItem(placement: .primaryAction) {
                    Button(action: toggleSync) {
                        Label(
                            clipboardManager.isRunning ? "Stop Sync" : "Start Sync",
                            systemImage: clipboardManager.isRunning ? "stop.circle" : "play.circle"
                        )
                    }
                    .buttonStyle(.borderedProminent)
                    .controlSize(.regular)
                }
            }
        }
        .frame(minWidth: 800, minHeight: 600)
        .background(.regularMaterial)
    }

    private var navigationTitle: String {
        switch selectedTab {
        case 0: return "Overview"
        case 1: return "Configuration"
        case 2: return "Activity Log"
        default: return "Overview"
        }
    }

    private func toggleSync() {
        if clipboardManager.isRunning {
            clipboardManager.stopMonitoring()
            networkManager.disconnect()
        } else {
            clipboardManager.startMonitoring()
            if configManager.config.receiver.enabled {
                networkManager.connect()
            }
        }
    }
}
