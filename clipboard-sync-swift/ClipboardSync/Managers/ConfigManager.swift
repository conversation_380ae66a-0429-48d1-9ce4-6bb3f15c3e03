import Foundation
import Yams

class ConfigManager: ObservableObject {
    @Published var config = AppConfig()
    
    private let userDefaults = UserDefaults.standard
    private let configKey = "ClipboardSyncConfig"
    
    init() {
        loadConfiguration()
    }
    
    func loadConfiguration() {
        NSLog("ConfigManager: Starting configuration loading...")

        // 首先尝试从YAML文件加载（优先使用最新的配置文件）
        if loadFromYAMLFile() {
            NSLog("ConfigManager: Configuration loaded from YAML file")
            return
        }

        // 如果YAML文件加载失败，尝试从UserDefaults加载
        if let data = userDefaults.data(forKey: configKey),
           let decodedConfig = try? JSONDecoder().decode(AppConfig.self, from: data) {
            config = decodedConfig
            NSLog("ConfigManager: Configuration loaded from UserDefaults")
        } else {
            NSLog("ConfigManager: Using default configuration (no saved config found)")
        }
    }
    
    func saveConfiguration() -> Bool {
        do {
            let data = try JSONEncoder().encode(config)
            userDefaults.set(data, forKey: configKey)
            return true
        } catch {
            print("Failed to save configuration: \(error)")
            return false
        }
    }
    
    func loadFromYAMLFile() -> Bool {
        NSLog("ConfigManager: Starting YAML file loading process...")

        // 尝试从多个可能的位置加载YAML配置文件
        let possiblePaths = [
            // 相对于应用程序的路径
            Bundle.main.path(forResource: "config", ofType: "yaml"),
            // 项目根目录的config文件夹
            findProjectConfigPath(),
            // 用户主目录
            NSHomeDirectory() + "/.clipboard-sync/config.yaml"
        ]

        NSLog("ConfigManager: Checking paths for YAML config file:")
        for (index, path) in possiblePaths.enumerated() {
            if let path = path {
                NSLog("  [\(index)] \(path)")
                if loadYAMLFromPath(path) {
                    NSLog("ConfigManager: Successfully loaded config from: \(path)")
                    return true
                }
            } else {
                NSLog("  [\(index)] (nil path)")
            }
        }

        NSLog("ConfigManager: Failed to load config from any YAML file")
        return false
    }
    
    private func findProjectConfigPath() -> String? {
        // 尝试找到项目根目录的config.yaml文件
        let currentPath = FileManager.default.currentDirectoryPath

        // 获取应用程序bundle的路径，用于更准确的路径解析
        let bundlePath = Bundle.main.bundlePath
        let bundleParent = (bundlePath as NSString).deletingLastPathComponent

        let possiblePaths = [
            // 从当前工作目录开始查找
            currentPath + "/config/config.yaml",
            currentPath + "/../config/config.yaml",
            currentPath + "/../../config/config.yaml",
            // 从bundle路径开始查找
            bundleParent + "/config/config.yaml",
            bundleParent + "/../config/config.yaml",
            bundleParent + "/../../config/config.yaml",
            bundleParent + "/../../../config/config.yaml",
            // 绝对路径尝试（基于项目结构）
            "/Users/<USER>/Documents/GitHub/clipboard-sync-ntfy/config/config.yaml"
        ]

        NSLog("ConfigManager: Searching for config file in the following paths:")
        for path in possiblePaths {
            NSLog("  - \(path)")
            if FileManager.default.fileExists(atPath: path) {
                NSLog("  ✓ Found config file at: \(path)")
                return path
            }
        }

        NSLog("ConfigManager: No config file found in any of the searched paths")
        return nil
    }
    
    private func loadYAMLFromPath(_ path: String) -> Bool {
        print("ConfigManager: Attempting to load YAML from: \(path)")

        guard FileManager.default.fileExists(atPath: path) else {
            print("ConfigManager: File does not exist at path: \(path)")
            return false
        }

        do {
            let yamlString = try String(contentsOfFile: path, encoding: .utf8)
            print("ConfigManager: Successfully read YAML file content (\(yamlString.count) characters)")

            let yamlData = try Yams.load(yaml: yamlString) as? [String: Any]

            if let yamlData = yamlData {
                print("ConfigManager: Successfully parsed YAML data")
                let oldConfig = config
                config = parseYAMLToConfig(yamlData)

                // 打印配置变化
                print("ConfigManager: Configuration updated:")
                print("  Sender enabled: \(oldConfig.sender.enabled) -> \(config.sender.enabled)")
                print("  Sender URL: \(oldConfig.sender.ntfyTopicUrl) -> \(config.sender.ntfyTopicUrl)")
                print("  Receiver enabled: \(oldConfig.receiver.enabled) -> \(config.receiver.enabled)")
                print("  Receiver topic: \(oldConfig.receiver.ntfyTopic) -> \(config.receiver.ntfyTopic)")

                // 保存到UserDefaults
                if saveConfiguration() {
                    print("ConfigManager: Configuration saved to UserDefaults")
                } else {
                    print("ConfigManager: Failed to save configuration to UserDefaults")
                }
                return true
            } else {
                print("ConfigManager: Failed to parse YAML data as dictionary")
            }
        } catch {
            print("ConfigManager: Failed to load YAML from \(path): \(error)")
        }

        return false
    }
    
    private func parseYAMLToConfig(_ yaml: [String: Any]) -> AppConfig {
        var newConfig = AppConfig()
        
        // 解析sender配置
        if let sender = yaml["sender"] as? [String: Any] {
            newConfig.sender.enabled = sender["enabled"] as? Bool ?? false
            newConfig.sender.ntfyTopicUrl = sender["ntfy_topic_url"] as? String ?? ""
            newConfig.sender.pollIntervalSeconds = sender["poll_interval_seconds"] as? Double ?? 1.0
            newConfig.sender.requestTimeoutSeconds = sender["request_timeout_seconds"] as? Int ?? 15
            newConfig.sender.filenamePrefix = sender["filename_prefix"] as? String ?? "clipboard_content_"
        }
        
        // 解析receiver配置
        if let receiver = yaml["receiver"] as? [String: Any] {
            newConfig.receiver.enabled = receiver["enabled"] as? Bool ?? false
            newConfig.receiver.ntfyServer = receiver["ntfy_server"] as? String ?? "ntfy.sh"
            newConfig.receiver.ntfyTopic = receiver["ntfy_topic"] as? String ?? ""
            newConfig.receiver.reconnectDelaySeconds = receiver["reconnect_delay_seconds"] as? Int ?? 5
            newConfig.receiver.requestTimeoutSeconds = receiver["request_timeout_seconds"] as? Int ?? 15
        }
        
        // 解析logging配置
        if let logging = yaml["logging"] as? [String: Any] {
            newConfig.logging.level = logging["level"] as? String ?? "INFO"
        }
        
        // 解析macOS配置
        if let macos = yaml["macos"] as? [String: Any] {
            newConfig.macos.imageSupport = macos["image_support"] as? Bool ?? true
        }
        
        return newConfig
    }
    
    func exportToYAML() -> String {
        let yamlDict: [String: Any] = [
            "sender": [
                "enabled": config.sender.enabled,
                "ntfy_topic_url": config.sender.ntfyTopicUrl,
                "poll_interval_seconds": config.sender.pollIntervalSeconds,
                "request_timeout_seconds": config.sender.requestTimeoutSeconds,
                "filename_prefix": config.sender.filenamePrefix
            ],
            "receiver": [
                "enabled": config.receiver.enabled,
                "ntfy_server": config.receiver.ntfyServer,
                "ntfy_topic": config.receiver.ntfyTopic,
                "reconnect_delay_seconds": config.receiver.reconnectDelaySeconds,
                "request_timeout_seconds": config.receiver.requestTimeoutSeconds
            ],
            "logging": [
                "level": config.logging.level
            ],
            "macos": [
                "image_support": config.macos.imageSupport
            ]
        ]
        
        do {
            return try Yams.dump(object: yamlDict)
        } catch {
            return "# Failed to export configuration: \(error)"
        }
    }
}

// MARK: - Configuration Models

struct AppConfig: Codable {
    var sender = SenderConfig()
    var receiver = ReceiverConfig()
    var logging = LoggingConfig()
    var macos = MacOSConfig()
}

struct SenderConfig: Codable {
    var enabled = true
    var ntfyTopicUrl = "https://ntfy.sh/YOUR_SEND_TOPIC_HERE"
    var pollIntervalSeconds = 1.0
    var requestTimeoutSeconds = 15
    var filenamePrefix = "clipboard_content_"
}

struct ReceiverConfig: Codable {
    var enabled = true
    var ntfyServer = "ntfy.sh"
    var ntfyTopic = "YOUR_RECEIVE_TOPIC_HERE"
    var reconnectDelaySeconds = 5
    var requestTimeoutSeconds = 15
}

struct LoggingConfig: Codable {
    var level = "INFO"
}

struct MacOSConfig: Codable {
    var imageSupport = true
}
